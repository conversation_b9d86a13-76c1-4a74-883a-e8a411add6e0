# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx4096m -Dfile.encoding=UTF-8 -XX:+UseG1GC -XX:MaxMetaspaceSize=1g
# When configured, Gradle will run in incubating parallel mode.
# This option should only be used with decoupled projects. For more details, visit
# https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects
# org.gradle.parallel=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true

# 国内网络优化配置
# 启用并行构建以提高速度
org.gradle.parallel=true
# 启用构建缓存
org.gradle.caching=true
# 启用按需配置
org.gradle.configureondemand=true
# 启用Gradle守护进程
org.gradle.daemon=true

# 并行下载配置
# 设置最大并行工作线程数（根据CPU核心数调整）
org.gradle.workers.max=15
# 启用并行依赖解析
org.gradle.parallel.threads=15

# 网络优化配置
# 增加网络超时时间（针对国内网络环境）
systemProp.http.socketTimeout=180000
systemProp.http.connectionTimeout=60000
systemProp.https.socketTimeout=180000
systemProp.https.connectionTimeout=60000
# 启用HTTP/2以提高下载效率
systemProp.http.keepAlive=true
systemProp.http.maxConnections=10
systemProp.http.maxConnectionsPerRoute=5

# Maven下载优化
systemProp.maven.wagon.http.pool=true
systemProp.maven.wagon.httpconnectionManager.ttlSeconds=120
systemProp.maven.wagon.http.retryHandler.count=3

# 抑制compileSdk警告
android.suppressUnsupportedCompileSdk=36

# 启用Jetifier以支持旧版本库
android.enableJetifier=true

# 缓存和构建优化
# 启用增量编译
kotlin.incremental=true
# 启用增量注解处理
kapt.incremental.apt=true
# 使用增量Java编译
org.gradle.java.compile-classpath-packaging=true

# 解决缓存问题
org.gradle.unsafe.configuration-cache=false
org.gradle.configuration-cache.problems=warn
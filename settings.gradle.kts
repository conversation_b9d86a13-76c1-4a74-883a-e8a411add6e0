pluginManagement {
    repositories {
        // 腾讯云镜像源（您偏好的选择，优先使用）
        maven {
            url = uri("https://mirrors.cloud.tencent.com/nexus/repository/maven-public/")
            name = "TencentCloudPlugin"
        }

        // 阿里云镜像源（备用加速）
        maven {
            url = uri("https://maven.aliyun.com/repository/gradle-plugin")
            name = "AliYunPlugin"
        }
        maven {
            url = uri("https://maven.aliyun.com/repository/google")
            name = "AliYunGooglePlugin"
        }

        // 原始仓库（最后备用）
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        // 腾讯云镜像源（您偏好的选择，优先使用）
        maven {
            url = uri("https://mirrors.cloud.tencent.com/nexus/repository/maven-public/")
            name = "TencentCloud"
        }

        // 阿里云镜像源（备用加速）
        maven {
            url = uri("https://maven.aliyun.com/repository/public")
            name = "AliYun"
        }
        maven {
            url = uri("https://maven.aliyun.com/repository/google")
            name = "AliYunGoogle"
        }
        maven {
            url = uri("https://maven.aliyun.com/repository/central")
            name = "AliYunCentral"
        }

        // 原始仓库（最后备用）
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral {
            content {
                includeGroupByRegex("org\\.tensorflow.*")
                includeGroupByRegex("org\\.jetbrains.*")
            }
        }
    }
}

rootProject.name = "Uno"
include(":app")

// 启用并行下载和构建优化
gradle.startParameter.apply {
    // 启用并行项目执行
    isParallelProjectExecutionEnabled = true
    // 设置最大工作线程数
    maxWorkerCount = Runtime.getRuntime().availableProcessors()
}